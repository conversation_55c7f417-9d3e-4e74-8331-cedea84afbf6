import { Ionicons } from "@expo/vector-icons";
import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import React, { useState, useEffect, useMemo } from "react";
import {
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  TextInput,
  View,
  ViewStyle,
} from "react-native";
import globalStyles from "@/lib/globalStyles";
import { screenWidth } from "@/lib/device";

// Configure dayjs with custom parse format plugin
dayjs.extend(customParseFormat);

const formatDateSafely = (dateValue?: Date): string | null => {
  if (!dateValue) return null;
  try {
    const dayjsDate = dayjs(dateValue);
    return dayjsDate.isValid() ? dayjsDate.format("DD-MM-YYYY") : null;
  } catch (error) {
    return null;
  }
};

const parseDateFromText = (text: string): Date | null => {
  if (!text || text.length !== 10) return null;
  const parsed = dayjs(text, "DD-MM-YYYY", true);
  return parsed.isValid() ? parsed.toDate() : null;
};

export default function MyDatePicker({
  date,
  label,
  onChange,
  style,
  theme = "primary",
  maximumDate,
  minimumDate,
  defaultValue,
  required = false,
}: {
  date?: Date;
  label: string;
  onChange: (newDate: Date) => void;
  style?: StyleProp<ViewStyle>;
  size?: "sm" | "md";
  theme?: "primary" | "secondary";
  maximumDate?: Date;
  minimumDate?: Date;
  defaultValue?: Date;
  required?: boolean;
}) {
  const [show, setShow] = useState(false);
  const [textValue, setTextValue] = useState(
    () => formatDateSafely(date) || formatDateSafely(defaultValue) || undefined
  );

  useEffect(() => {
    setTextValue(
      formatDateSafely(date) || formatDateSafely(defaultValue) || undefined
    );
  }, [date, defaultValue]);

  const handleDateChange = (e: DateTimePickerEvent) => {
    if (e.type === "dismissed") return setShow(false);

    if (e.nativeEvent.timestamp) {
      const newDate = new Date(e.nativeEvent.timestamp);
      onChange(newDate);
    }

    setShow(false);
  };

  const handleTextChange = (text: string) => {
    setTextValue(text);

    if (text.length !== 10) return;
    const parsedDate = parseDateFromText(text);
    if (!parsedDate) return;
    onChange(parsedDate);
  };

  const getThemeStyle = () => {
    switch (theme) {
      case "secondary":
        return styles.secondaryTheme;
      default:
        return styles.primaryTheme;
    }
  };

  const getTextStyle = () => {
    return theme === "primary" ? styles.primaryText : styles.secondaryText;
  };

  const getIconColor = () => {
    return theme === "primary"
      ? globalStyles.colors.primary1
      : globalStyles.colors.primary1;
  };

  return (
    <View style={[styles.container, style]}>
      {required && <View style={styles.required} />}
      {date && <Text style={styles.label}>{label}</Text>}
      <View style={[styles.inputContainer, getThemeStyle()]}>
        <TextInput
          style={[styles.textInput, getTextStyle()]}
          value={textValue}
          onChangeText={handleTextChange}
          placeholder="Ex: 01-01-2000"
          maxLength={10}
          keyboardType="numeric"
          placeholderTextColor={}
        />
        <Pressable style={styles.calendarButton} onPress={() => setShow(true)}>
          <Ionicons name="calendar-outline" size={18} color={getIconColor()} />
        </Pressable>
      </View>
      {show && (
        <DateTimePicker
          value={date || defaultValue || new Date()}
          mode="date"
          is24Hour={true}
          locale="pt-BR"
          onChange={handleDateChange}
          maximumDate={maximumDate}
          minimumDate={minimumDate}
          accentColor={globalStyles.colors.primary1}
          textColor={globalStyles.rgba().tertiary2}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "relative",
  },
  required: {
    position: "absolute",
    top: 0,
    left: 0,
    width: screenWidth * 0.015,
    height: screenWidth * 0.015,
    borderRadius: globalStyles.rounded.xs,
    backgroundColor: globalStyles.colors.primary2,
    zIndex: 1,
  },
  button: {
    borderRadius: globalStyles.rounded.full,
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    gap: globalStyles.gap["2xs"],
  },
  label: {
    position: "absolute",
    top: -18,
    fontSize: globalStyles.size.sm,
    color: globalStyles.colors.tertiary2,
  },
  primaryTheme: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
  },
  secondaryTheme: {
    backgroundColor: globalStyles.colors.white,
  },
  text: {
    flex: 1,
    fontSize: globalStyles.size.lg,
    lineHeight: globalStyles.size.textAdjustLineHeight,
  },
  primaryText: {
    color: globalStyles.colors.primary1,
  },
  secondaryText: {
    color: globalStyles.rgba().primary1,
  },
  smallSize: {
    paddingHorizontal: globalStyles.gap["2xs"],
    paddingVertical: 5,
  },
  mediumSize: {
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: 8,
  },
  smallTextSize: {
    fontSize: globalStyles.size.md,
  },
  mediumTextSize: {
    fontSize: globalStyles.size.lg,
  },
  inputContainer: {
    borderRadius: globalStyles.rounded.full,
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: 1,
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    gap: globalStyles.gap["2xs"],
  },
  textInput: {
    flex: 1,
    fontSize: globalStyles.size.lg,
    color: globalStyles.rgba().primary1,
  },
  calendarButton: {
    justifyContent: "center",
    alignItems: "center",
  },
});
